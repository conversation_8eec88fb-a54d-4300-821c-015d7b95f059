{"description": "公司介绍小程序", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": false, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.6", "appid": "wx7e71cbf2f13e24ec", "projectname": "公司介绍小程序", "condition": {}, "simulatorPluginLibVersion": {}, "editorSetting": {}}