<!--pages/employee-detail/employee-detail.wxml-->
<view class="employee-detail-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 员工详情内容 -->
  <view class="detail-content" wx:else>
    <view class="section_1">
      <image src="../../images/lanhu_mingpian1/pso2kcaravxuvayjpxt2zhrmian159sxl287ef2b0-d898-4a8c-bdc9-956cffdfc632.png" class="image_1"></image>
      <text lines="1" class="text_1">{{employee.workLocation}}</text>
    </view>
    <view class="section_2">
      <view class="box_1">
        <text lines="1" decode="true" class="text_2">{{employee.name}}</text>
        <text lines="1" decode="true" class="text_3">{{employee.department}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{employee.position}}</text>
        <image src="../../images/lanhu_mingpian1/ps0kfwj3alldrklbfog4i1ggsyf229uq5a20e5e0fe-48df-458d-b754-a47f4df7b563.png" class="image_2"></image>
        <text lines="1" class="text_4">地址：{{employee.address}}</text>
        <text lines="1" class="text_5">电话：{{employee.phone}}</text>
        <text lines="1" class="text_6">手机：{{employee.avatar}}</text>
        <view class="group_1">
          <text lines="1" class="text_7">传真：{{employee.id}}</text>
          <view class="text-wrapper_1">
            <text lines="1" decode="true" class="text_8">&nbsp;&nbsp;</text>
            <text lines="1" decode="true" class="text_9">邮编: {{employee.manager}}&nbsp;</text>
          </view>
        </view>
        <text lines="1" decode="true" class="text_10">邮箱：{{employee.email}}</text>
        <image src="../../images/lanhu_mingpian1/psvknu459cecqwoot05qjetja0nt1ueqbq40989e62b-4e24-41ec-9d3e-e05ba9f586e0.png" class="image_3"></image>
        <image src="../../images/lanhu_mingpian1/pszaarhyu5splis8z4xb99xogp03mhncadb954c88-6d50-4c21-bccc-0b21dfd78cc3.png" class="image_4"></image>
        <image src="../../images/lanhu_mingpian1/psyfn1e0nep5l14xj3dwkmwtjs2gs7tru6d3314d4b5-103c-4a1e-a9d5-f5c61e29e2d0.png" class="image_5"></image>
        <image src="../../images/lanhu_mingpian1/psyquoanxchiazzctd19j9rjirhihl0uomh6c7a3371-035d-4642-96fe-f620aec2eba2.png" class="image_6"></image>
        <image src="../../images/lanhu_mingpian1/pspcdy0uy4zcj1eh07yokanv4qawnpwdjgya4bcbccf-7505-49d5-bebe-c4334dedee9b.png" class="image_7"></image>
        <image src="../../images/lanhu_mingpian1/pswoc38emebygfmuto8jjm3ep9u1hb2eou4205cb3c-9f29-4f67-b05e-49c36b6db0ea.png" class="image_8"></image>
        <image src="../../images/lanhu_mingpian1/pszo35kz6l4po1abgfcv9fgmqi0v6imr7d9ad2eb630-a51e-4d32-beea-c6757151451b.png" class="image_9"></image>
        <image src="../../images/lanhu_mingpian1/psjp6svwqegvk61ntxpa0sj2usbnx5k2lp5296030f-7764-4877-b8a1-5b38d8dafe51.png" class="image_10"></image>
        
      </view>
      <image src="../../images/lanhu_mingpian1/psusw9cs60e0bjs4ozsln9ums7p701zevu82d430ca7-ec16-4113-b099-3462736ec551.png" class="image_11"></image>
      <image src="../../images/lanhu_mingpian1/ps50mw71zeumhz422n723whjn6tiz9jsokic51c71a4-3fae-4032-9608-81b1454ac725.png" class="image_12"></image>
    </view>

    <!-- 功能按钮组 -->
    <view class="action-buttons">
      <button class="action-btn" bindtap="handlePhoneCall">
        <text>打电话</text>
      </button>
      <button class="action-btn" bindtap="handleShareCard">
        <text>分享名片</text>
      </button>
      <button class="action-btn" bindtap="handleSaveContact">
        <text>存入通讯录</text>
      </button>
    </view>

    <!-- 长图展示 -->
    <view class="long-image-section" wx:if="{{longImageData}}">
      <view class="long-image-container">
        <image class="long-image" src="{{longImageData.imageUrl}}" mode="widthFix" bindload="onLongImageLoad" binderror="onLongImageError" show-menu-by-longpress="{{true}}"></image>
      </view>
    </view>

    <!-- 长图加载状态 -->
    <view class="long-image-loading" wx:if="{{longImageLoading}}">
      <text class="loading-text">加载长图中...</text>
    </view>

  </view>
</view>