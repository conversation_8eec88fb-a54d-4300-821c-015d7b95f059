<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="logo-section">
      <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 登录表单 -->
  <view class="form-container">
    <view class="form-card">
      <view class="form-title">
        <text class="title-text">员工登录</text>
        <text class="subtitle-text">Employee Login</text>
      </view>

      <form bindsubmit="handleLogin">
        <view class="form-group">
          <label class="form-label">用户名/工号</label>
          <input 
            class="form-input" 
            type="text" 
            placeholder="请输入用户名或工号"
            value="{{username}}"
            bindinput="onUsernameInput"
            maxlength="20"
          />
        </view>

        <view class="form-group">
          <label class="form-label">密码</label>
          <input 
            class="form-input" 
            type="password" 
            placeholder="请输入密码"
            value="{{password}}"
            bindinput="onPasswordInput"
            maxlength="20"
          />
        </view>

        <view class="form-group">
          <button 
            class="login-btn {{isLoading ? 'loading' : ''}}" 
            form-type="submit"
            disabled="{{isLoading || !canSubmit}}"
          >
            <text wx:if="{{!isLoading}}">登录</text>
            <text wx:else>登录中...</text>
          </button>
        </view>
      </form>

      <!-- 提示信息 -->
      <view class="tips">
        <text class="tips-text">请使用公司分配的账号密码登录</text>
      </view>
    </view>
  </view>

</view>
