<!--pages/employee-list/employee-list.wxml-->
<view class="employee-list-container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="welcome-section">
      <text class="welcome-text">欢迎，{{userInfo.name || '员工'}}</text>
      <text class="department-text">{{userInfo.department || ''}}</text>
    </view>
    <view class="logout-section">
      <button class="logout-btn" bindtap="handleLogout">退出</button>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-section">
    <view class="filter-box">
      <!-- 一级部门选择 -->
      <view class="filter-item">
        <picker
          class="filter-picker"
          mode="selector"
          range="{{level1DepartmentOptions}}"
          value="{{selectedLevel1Index}}"
          bindchange="onLevel1DepartmentChange"
        >
          <view class="picker-content">
            <text class="picker-text">{{selectedLevel1Department || '选择一级部门'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 二级部门选择 -->
      <view class="filter-item" wx:if="{{level2DepartmentOptions.length > 1}}">
        <picker
          class="filter-picker"
          mode="selector"
          range="{{level2DepartmentOptions}}"
          value="{{selectedLevel2Index}}"
          bindchange="onLevel2DepartmentChange"
        >
          <view class="picker-content">
            <text class="picker-text">{{selectedLevel2Department || '选择二级部门'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 三级部门选择 -->
      <view class="filter-item" wx:if="{{level3DepartmentOptions.length > 1}}">
        <picker
          class="filter-picker"
          mode="selector"
          range="{{level3DepartmentOptions}}"
          value="{{selectedLevel3Index}}"
          bindchange="onLevel3DepartmentChange"
        >
          <view class="picker-content">
            <text class="picker-text">{{selectedLevel3Department || '选择三级部门'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 清除筛选按钮 -->
    <view class="clear-filter-btn" wx:if="{{selectedLevel1Department || selectedLevel2Department || selectedLevel3Department}}" bindtap="clearFilters">
      <text class="clear-text">清除筛选</text>
    </view>
  </view>

  <!-- 员工列表 -->
  <view class="list-section">
    <view class="list-header">
      <text class="list-title">员工名录</text>
      <text class="list-count">共 {{filteredEmployees.length}} 人</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{filteredEmployees.length === 0}}">
      <text class="empty-text">{{(selectedDepartment || selectedPosition) ? '未找到符合条件的员工' : '暂无员工数据'}}</text>
    </view>

    <!-- 员工卡片列表 -->
    <scroll-view
      class="employee-scroll"
      scroll-y="true"
      wx:else
    >
      <view
        class="employee-card"
        wx:for="{{filteredEmployees}}"
        wx:key="id"
        bindtap="goToDetail"
        data-employee="{{item}}"
      >
        <view class="card-header">
          <view class="avatar-section">
            <image
              class="avatar"
              src="{{item.avatar || '/images/default-avatar.png'}}"
              mode="aspectFill"
            ></image>
          </view>
          <view class="info-section">
            <text class="name">{{item.name}}</text>
            <text class="employee-id">工号：{{item.employeeId}}</text>
          </view>
          <view class="arrow-section">
            <text class="arrow">></text>
          </view>
        </view>

        <view class="card-body">
          <view class="info-row">
            <text class="label">部门：</text>
            <text class="value">{{item.departmentPath || item.department}}</text>
          </view>
          <view class="info-row">
            <text class="label">职位：</text>
            <text class="value">{{item.position}}</text>
          </view>
          <view class="info-row" wx:if="{{item.phone}}">
            <text class="label">电话：</text>
            <text class="value phone">{{item.phone}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
