// pages/index/index.js
const { api } = require('../../utils/api')

Page({
  data: {
    backgroundImageUrl: '', // 默认背景图
    loading: true
  },

  onLoad() {
    this.loadBackgroundImage()
  },

  onShow() {
    // 每次显示页面时检查登录状态
    const app = getApp()
    if (app.globalData.isLoggedIn) {
      // 如果已登录，直接跳转到员工列表页
      wx.redirectTo({
        url: '/pages/employee-list/employee-list'
      })
    }
  },

  /**
   * 加载背景图片
   */
  async loadBackgroundImage() {
    try {
      wx.showLoading({
        title: '加载中...'
      })

      const result = await api.getBackgroundImage()

      if (result.data && result.data.imageUrl) {
        this.setData({
          backgroundImageUrl: result.data.imageUrl,
          loading: false
        })
      } else {
        // 使用默认图片
        this.setData({
          loading: false
        })
      }
    } catch (error) {
      console.error('加载背景图片失败:', error)
      // 使用默认图片
      this.setData({
        loading: false
      })

      wx.showToast({
        title: '图片加载失败，使用默认背景',
        icon: 'none',
        duration: 2000
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 图片加载成功
   */
  onImageLoad() {
    console.log('背景图片加载成功')
    this.setData({
      loading: false
    })
  },

  /**
   * 图片加载失败
   */
  onImageError() {
    console.error('背景图片加载失败')
    this.setData({
      backgroundImageUrl: '/images/default-bg.jpg',
      loading: false
    })

    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    })
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  /**
   * 跳转到长图展示页面
   */
  goToLongImages() {
    wx.navigateTo({
      url: '/pages/long-images/long-images'
    })
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '中国五矿 - 公司介绍',
      path: '/pages/index/index',
      imageUrl: this.data.backgroundImageUrl
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '中国五矿 - 公司介绍',
      imageUrl: this.data.backgroundImageUrl
    }
  }
})
