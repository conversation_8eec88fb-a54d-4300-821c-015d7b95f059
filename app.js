// app.js
App({
  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost:8080/api', // 后端API地址
    isLoggedIn: false,
    employeeInfo: null
  },

  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const employeeInfo = wx.getStorageSync('employeeInfo')
    
    if (token && employeeInfo) {
      this.globalData.isLoggedIn = true
      this.globalData.employeeInfo = employeeInfo
    }
  },

  // 登录
  login(employeeInfo, token) {
    this.globalData.isLoggedIn = true
    this.globalData.employeeInfo = employeeInfo
    wx.setStorageSync('token', token)
    wx.setStorageSync('employeeInfo', employeeInfo)
  },

  // 登出
  logout() {
    this.globalData.isLoggedIn = false
    this.globalData.employeeInfo = null
    wx.removeStorageSync('token')
    wx.removeStorageSync('employeeInfo')
  }
})
